{"name": "siptracker-clean", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:dev:ios": "eas build --profile development --platform ios", "build:dev:android": "eas build --profile development --platform android", "build:preview:ios": "eas build --profile preview --platform ios", "build:preview:android": "eas build --profile preview --platform android", "build:prod:ios": "eas build --profile production --platform ios", "build:prod:android": "eas build --profile production --platform android", "build:all:dev": "eas build --profile development --platform all", "build:all:preview": "eas build --profile preview --platform all", "build:all:prod": "eas build --profile production --platform all", "submit:ios": "eas submit --platform ios", "submit:android": "eas submit --platform android", "update": "eas update", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "test": "jest", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean", "prebuild:android": "expo prebuild --platform android", "prebuild:ios": "expo prebuild --platform ios", "doctor": "expo doctor", "install:clean": "rm -rf node_modules && npm install", "reset:cache": "expo start --clear", "check:permissions": "node -e \"console.log('Checking app permissions configuration...')\"", "setup:env": "cp .env.example .env", "android:build": "cd android && ./gradlew assembleDebug", "android:release": "cd android && ./gradlew assembleRelease", "android:clean": "cd android && ./gradlew clean"}, "dependencies": {"@expo-google-fonts/poppins": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@shopify/react-native-skia": "v2.0.0-next.4", "@supabase/supabase-js": "^2.50.0", "expo": "53.0.12", "expo-application": "^6.1.4", "expo-calendar": "^14.1.4", "expo-camera": "^16.1.8", "expo-constants": "^17.1.6", "expo-contacts": "^14.2.5", "expo-crypto": "^14.1.5", "expo-device": "^7.1.4", "expo-font": "~13.3.1", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "^7.1.5", "expo-local-authentication": "^16.0.4", "expo-location": "^18.1.5", "expo-media-library": "^17.1.7", "expo-notifications": "^0.31.3", "expo-secure-store": "^14.2.3", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.15", "lottie-react-native": "^7.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "victory-native": "^41.17.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "latest", "react-native-svg-transformer": "^1.5.1"}, "private": true}